<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { clearOrderSession, resolveVisitSessionKey } from '@/utils/sessionHelpers'
import SubscriptionItemSkeleton from '@/views/user/components/SubscriptionItemSkeleton.vue'
import {
  IconBolt,
  IconCheck,
  IconChevronRight,
  IconClockExclamation,
  IconInfoCircle,
  IconPlayerPause,
  IconX,
  IconRefresh,
  IconExclamationTriangle,
  IconCircleDot,
} from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { computed, onMounted, ref, nextTick } from 'vue'
import { toast } from 'vue-sonner'
import { useWLQuestions } from '@/store/wlQuestions'
import { useSubscriptionStore } from '@/store/subscription'
import { useActiveSubscriptionPlan } from '@/composables/useActiveSubscriptionPlan'

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const { resetQuestions: resetWLQuestions } = useWLQuestions()

const {
  validateActiveSubscriptionPlan,
  isSubscriptionActive,
  isTreatmentAvailable,
  getTreatmentType,
  getLastSubscriptionId,
} = useActiveSubscriptionPlan()

const subscriptionStore = useSubscriptionStore()
const { btnReactivateLoading } = storeToRefs(subscriptionStore)
const { reactivateSubscription } = subscriptionStore

const edVisitSession = useSessionStorage('edVisitSession', {})
const hlVisitSession = useSessionStorage('hlVisitSession', {})

// Loading states
const skeletonLoading = ref(false)
const btnLoading = ref(null)
const pastSkeletonLoading = ref(false)
const btnRenewPrescriptionLoading = ref(null)

// Data states
const activeSubscriptions = ref([])
const pastSubscriptions = ref([])
const pastCurrentPage = ref(1)
const pastTotalPage = ref(0)
const pastTotalRecords = ref(0)

// Modal states
const isRestartModalVisible = ref(false)
const subscriptionToRestart = ref(null)

// Error handling states
const apiErrors = ref({
  activeSubscriptions: null,
  pastSubscriptions: null,
  treatmentStart: null,
  prescriptionRenewal: null,
  subscriptionReactivation: null,
})

const retryAttempts = ref({
  activeSubscriptions: 0,
  pastSubscriptions: 0,
})

// Constants for retry logic
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY = 1000

// Utility functions
function announceToScreenReader(message) {
  const announcement = document.createElement('div')

  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  document.body.appendChild(announcement)

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

const startEDTooltip = computed(() => {
  if (!isTreatmentAvailable('ed')) {
    return 'ED treatment is not available in your state.'
  }
  if (isSubscriptionActive('ed')) {
    return 'You already have an active subscription for ED treatment.'
  }

  return ''
})

const startHLTooltip = computed(() => {
  if (!isTreatmentAvailable('hl')) {
    return 'Hair treatment is not available in your state.'
  }
  if (isSubscriptionActive('hl')) {
    return 'You already have an active subscription for Hair treatment.'
  }

  return ''
})

const startWLTooltip = computed(() => {
  if (!isTreatmentAvailable('wl')) {
    return 'Weight loss treatment is not available in your state.'
  }
  if (isSubscriptionActive('wl')) {
    return 'You already have an active subscription for Weight loss treatment.'
  }

  return ''
})

async function startTreatment(category) {
  try {
    btnLoading.value = category
    apiErrors.value.treatmentStart = null

    // Clear any existing order session
    clearOrderSession()

    // Announce action to screen readers
    announceToScreenReader(`Starting ${category.toUpperCase()} treatment process`)

    if (category === 'wl') {
      resetWLQuestions()
      await router.push({ name: `${category}-questions`, params: { slug: 'start' } })

      return
    }

    const visitSession = useSessionStorage(resolveVisitSessionKey(category), {})
    const treatmentType = getTreatmentType(category)

    if (treatmentType === 'followup') {
      visitSession.value = {}
      visitSession.value['subscriptionId'] = getLastSubscriptionId(category)
      visitSession.value['visitType'] = 'followup'

      await router.push({ name: `${category}-followup-questions`, params: { slug: 'start' } })
    } else if (treatmentType === 'new') {
      visitSession.value = {}
      visitSession.value['visitType'] = 'new'

      await router.push({ name: `${category}-questions`, params: { slug: 'start' } })
    } else {
      throw new Error(`Invalid treatment type: ${treatmentType}`)
    }
  } catch (error) {
    console.error('Error starting treatment:', error)

    const errorMessage = `Failed to start ${category.toUpperCase()} treatment. Please try again.`

    apiErrors.value.treatmentStart = errorMessage

    toast.error(errorMessage, {
      action: {
        label: 'Retry',
        onClick: () => startTreatment(category),
      },
    })
  } finally {
    btnLoading.value = null
  }
}

async function fetchActiveSubscriptions(isRetry = false) {
  try {
    if (!isRetry) {
      skeletonLoading.value = true
      apiErrors.value.activeSubscriptions = null
    }

    const { data } = await ApiService.post('/active-subscriptions')

    if (data.status === 200) {
      activeSubscriptions.value = data.activeSubscriptions
      retryAttempts.value.activeSubscriptions = 0

      // Announce to screen readers
      nextTick(() => {
        const message = `Loaded ${data.activeSubscriptions.length} active subscriptions`

        announceToScreenReader(message)
      })
    } else {
      throw new Error(data.message || 'Failed to fetch active subscriptions')
    }
  } catch (error) {
    console.error('Error fetching active subscriptions:', error)

    const errorMessage = processErrors(error)[0]

    apiErrors.value.activeSubscriptions = errorMessage

    // Only show toast for non-retry attempts to avoid spam
    if (!isRetry) {
      toast.error(errorMessage, {
        action: retryAttempts.value.activeSubscriptions < MAX_RETRY_ATTEMPTS ? {
          label: 'Retry',
          onClick: () => retryFetchActiveSubscriptions(),
        } : undefined,
      })
    }
  } finally {
    if (!isRetry) {
      skeletonLoading.value = false
    }
  }
}

async function retryFetchActiveSubscriptions() {
  if (retryAttempts.value.activeSubscriptions >= MAX_RETRY_ATTEMPTS) {
    toast.error('Maximum retry attempts reached. Please refresh the page.')

    return
  }

  retryAttempts.value.activeSubscriptions++

  // Add exponential backoff delay
  const delay = RETRY_DELAY * Math.pow(2, retryAttempts.value.activeSubscriptions - 1)

  await new Promise(resolve => setTimeout(resolve, delay))

  await fetchActiveSubscriptions(true)
}

async function fetchPastSubscriptions(isRetry = false) {
  try {
    if (!isRetry) {
      if (pastCurrentPage.value === 1) {
        skeletonLoading.value = true
      } else {
        pastSkeletonLoading.value = true
      }
      apiErrors.value.pastSubscriptions = null
    }

    const { data } = await ApiService.post('/past-subscriptions', { page: pastCurrentPage.value })

    if (data.status === 200) {
      if (pastCurrentPage.value === 1) {
        pastSubscriptions.value = data.pastSubscriptions.records
      } else {
        pastSubscriptions.value = [...pastSubscriptions.value, ...data.pastSubscriptions.records]
      }
      pastTotalPage.value = data.pastSubscriptions.totalPage
      pastTotalRecords.value = data.pastSubscriptions.totalRecords
      retryAttempts.value.pastSubscriptions = 0

      // Announce to screen readers
      nextTick(() => {
        const message = pastCurrentPage.value === 1
          ? `Loaded ${data.pastSubscriptions.records.length} past subscriptions`
          : `Loaded ${data.pastSubscriptions.records.length} more past subscriptions`

        announceToScreenReader(message)
      })
    } else {
      throw new Error(data.message || 'Failed to fetch past subscriptions')
    }
  } catch (error) {
    console.error('Error fetching past subscriptions:', error)

    const errorMessage = processErrors(error)[0]

    apiErrors.value.pastSubscriptions = errorMessage

    if (!isRetry) {
      toast.error(errorMessage, {
        action: retryAttempts.value.pastSubscriptions < MAX_RETRY_ATTEMPTS ? {
          label: 'Retry',
          onClick: () => retryFetchPastSubscriptions(),
        } : undefined,
      })
    }
  } finally {
    if (!isRetry) {
      skeletonLoading.value = false
      pastSkeletonLoading.value = false
    }
  }
}

async function retryFetchPastSubscriptions() {
  if (retryAttempts.value.pastSubscriptions >= MAX_RETRY_ATTEMPTS) {
    toast.error('Maximum retry attempts reached. Please refresh the page.')

    return
  }

  retryAttempts.value.pastSubscriptions++

  const delay = RETRY_DELAY * Math.pow(2, retryAttempts.value.pastSubscriptions - 1)

  await new Promise(resolve => setTimeout(resolve, delay))

  await fetchPastSubscriptions(true)
}

async function handlePastSubscriptionPagination() {
  if (!skeletonLoading.value && pastCurrentPage.value < pastTotalPage.value) {
    pastCurrentPage.value++
    await fetchPastSubscriptions()
  }
}

async function manageSubscription(subscriptionId) {
  router.push({ name: 'user-manage-subscription', params: { subscriptionId } })
}

function getSubscriptionStatus(status) {
  const statusDetails = {
    '0': {
      bgColor: 'bg-amber-500',
      textColor: 'text-amber-500',
      icon: IconPlayerPause,
    },
    '1': {
      bgColor: 'bg-lime-700',
      textColor: 'text-lime-700',
      icon: IconCheck,
    },
    '2': {
      bgColor: 'bg-red-600',
      textColor: 'text-red-600',
      icon: IconX,
    },
    '3': {
      bgColor: 'bg-black',
      textColor: 'text-black',
      icon: IconClockExclamation,
    },
    '4': {
      bgColor: 'bg-green-500',
      textColor: 'text-green-500',
      icon: IconCheck,
    },
  }

  return statusDetails[status] || {
    bgColor: 'bg-gray-200',
    textColor: 'text-gray-200',
    icon: IconCircleDot,
  }
}

async function handleRenewPrescription(subscription) {
  try {
    btnRenewPrescriptionLoading.value = subscription.id
    apiErrors.value.prescriptionRenewal = null

    // Announce action to screen readers
    announceToScreenReader(`Renewing prescription for ${subscription.product_name}`)

    const renewPrescriptionData = subscription.renewedPrescriptionData

    if (!isEmpty(renewPrescriptionData)) {
      if (subscription.category_name.toUpperCase() === 'ED') {
        edVisitSession.value = {
          visitType: 'renew',
          subscriptionId: subscription.id,

          // Additional data can be added here if needed
        }

        await router.push({ name: 'ed-questions-renew', params: { slug: 'start' } })

        return
      } else if (subscription.category_name.toUpperCase() === 'HL') {
        hlVisitSession.value = {
          visitType: 'renew',
          subscriptionId: subscription.id,

          // Additional data can be added here if needed
        }

        await router.push({ name: 'hl-questions-renew', params: { slug: 'start' } })

        return
      }
    }

    throw new Error('Prescription renewal data is not available')
  } catch (error) {
    console.error('Error renewing prescription:', error)

    const errorMessage = 'We are unable to renew your prescription at this time. Please try again later.'

    apiErrors.value.prescriptionRenewal = errorMessage

    toast.error(errorMessage, {
      action: {
        label: 'Retry',
        onClick: () => handleRenewPrescription(subscription),
      },
    })
  } finally {
    btnRenewPrescriptionLoading.value = null
  }
}

function startFollowupVisit(subscription) {
  if (subscription.category_name === 'WL') {
    resetWLQuestions()

    const wlVisitSession = useSessionStorage('wlVisitSession', {})

    wlVisitSession.value['subscriptionId'] = subscription.id
    wlVisitSession.value['activeIngredient'] = subscription.product_active_ingredient

    router.push({
      name: 'wl-followup-questions',
      params: { slug: 'start' },
    })
  } else if (subscription.category_name === 'ED') {
    const edVisitSession = useSessionStorage('edVisitSession', {})

    edVisitSession.value['subscriptionId'] = subscription.followup_visit_id

    router.push({
      name: 'ed-followup-questions',
      params: { slug: 'start' },
    })
  } else if (subscription.category_name === 'HL') {
    const hlVisitSession = useSessionStorage('hlVisitSession', {})

    hlVisitSession.value['subscriptionId'] = subscription.followup_visit_id

    router.push({
      name: 'hl-followup-questions',
      params: { slug: 'start' },
    })
  } else {
    console.error('Error: unknown treatment')
  }
}

onMounted(async () => {
  await Promise.all([
    validateActiveSubscriptionPlan(),
    fetchActiveSubscriptions(),
    fetchPastSubscriptions(),
  ])
})
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="flex flex-col self-center px-4 w-full max-w-[840px] max-md:max-w-full">
      <header>
        <h1
          id="page-title"
          class="text-3xl md:text-5xl text-black font-semibold py-6 mb-4"
        >
          Welcome, {{ userData?.first_name }}!
        </h1>
      </header>

      <div class="grid grid-cols-12 gap-5">
        <!-- skeleton loading -->
        <div
          v-if="skeletonLoading"
          class="col-span-12 space-y-5"
        >
          <div class="bg-white p-5 rounded-[20px] animate-pulse">
            <div class="h-6 bg-gray-300 rounded w-48 animate-pulse mb-3"></div>
            <div class="space-y-4">
              <SubscriptionItemSkeleton />
            </div>
          </div>

          <div class="flex flex-col md:flex-row gap-5">
            <!-- Sexual Health Card Skeleton -->
            <div class="flex-1 flex flex-col justify-between bg-white p-5 rounded-[20px] min-h-64 animate-pulse">
              <div>
                <div class="h-8 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
              <div class="flex -mt-6">
                <div class="h-10 bg-gray-200 rounded w-24"></div>
                <div class="w-fit ms-auto">
                  <div class="w-28 h-28 bg-gray-200 rounded object-cover"></div>
                </div>
              </div>
            </div>

            <!-- Weight Loss Card Skeleton -->
            <div class="flex-1 flex flex-col justify-between bg-white p-5 rounded-[20px] min-h-64 animate-pulse">
              <div>
                <div class="h-8 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
              <div class="flex -mt-6">
                <div class="h-10 bg-gray-200 rounded w-24"></div>
                <div class="w-fit ms-auto">
                  <div class="w-28 h-28 bg-gray-200 rounded object-cover"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- main content -->
        <div
          v-else
          class="col-span-12 space-y-5"
        >
          <!-- Error State for Active Subscriptions -->
          <div
            v-if="apiErrors.activeSubscriptions && activeSubscriptions.length === 0"
            class="bg-white p-5 rounded-[20px] border-l-4 border-red-500"
            role="alert"
            aria-live="polite"
          >
            <div class="flex items-start">
              <IconExclamationTriangle class="h-6 w-6 text-red-500 me-3 mt-1 flex-shrink-0" />
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-red-800 mb-2">
                  Failed to Load Active Subscriptions
                </h3>
                <p class="text-red-700 mb-4">
                  {{ apiErrors.activeSubscriptions }}
                </p>
                <TwButton
                  variant="secondary"
                  class="!border-red-500 !text-red-700 hover:!bg-red-50"
                  :loading="retryAttempts.activeSubscriptions > 0"
                  loading-text="Retrying..."
                  @click="retryFetchActiveSubscriptions"
                >
                  <IconRefresh class="h-4 w-4 me-2" />
                  Try Again
                </TwButton>
              </div>
            </div>
          </div>

          <!-- Active Subscriptions -->
          <div
            v-else-if="activeSubscriptions.length > 0"
            class="bg-white p-5 rounded-[20px]"
          >
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-2xl font-semibold text-gray-900">
                  Active Subscriptions
                </h2>
                <p class="text-sm text-gray-600 mt-1">
                  {{ activeSubscriptions.length }} active {{ activeSubscriptions.length === 1 ? 'subscription' : 'subscriptions' }}
                </p>
              </div>
              <div class="flex items-center gap-2 text-sm text-gray-500">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Live updates</span>
              </div>
            </div>
            <!-- List items -->
            <div
              class="space-y-4"
              role="list"
              aria-label="Active subscriptions"
            >
              <article
                v-for="subscription in activeSubscriptions"
                :key="subscription.id"
                class="px-3.5 py-4 rounded-3xl border !border-gray-400 max-md:max-w-full focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 transition-all duration-200 hover:shadow-md"
                role="listitem"
                :aria-label="`Active subscription for ${subscription.product_name}`"
              >
                <!--
                  <div
                  v-if="subscription.category_name.toUpperCase() === 'ED'"
                  class="bg-blue-200 text-blue-700 mb-2 rounded-2xl text-sm font-medium text-center py-1 px-2 md:absolute md:right-0 md:top-0 md:m-3"
                  >
                  Sexual Health
                  </div>
                  <div
                  v-if="subscription.category_name.toUpperCase() === 'HL'"
                  class="bg-green-200 text-green-700 mb-2 rounded-2xl text-sm font-medium text-center py-1 px-2 md:absolute md:right-0 md:top-0 md:m-3"
                  >
                  Hair Health
                  </div>
                -->
                <div class="grid grid-cols-10 gap-4">
                  <div class="col-span-10 md:col-span-2">
                    <div class="flex flex-col sm:items-center">
                      <div class="w-full bg-gray-100 flex justify-center rounded-2xl border !border-gray-200 mb-4">
                        <img
                          loading="lazy"
                          :src="subscription.product_img"
                          class="self-center aspect-square w-52 rounded-2xl"
                          alt=""
                        />
                      </div>
                      <div class="inline-flex gap-2 items-center">
                        <div
                          :class="`flex gap-2 w-fit px-3 py-2 font-semibold whitespace-nowrap rounded-full bg-opacity-20 border-2 ${getSubscriptionStatus(subscription.status).bgColor} ${getSubscriptionStatus(subscription.status).textColor} border-current`"
                          role="status"
                          :aria-label="`Subscription status: ${subscription.order_status}`"
                        >
                          <component
                            :is="getSubscriptionStatus(subscription.status).icon"
                            :class="`shrink-0 self-center w-[18px] h-[18px] text-white rounded-full p-px ${getSubscriptionStatus(subscription.status).bgColor}`"
                            stroke-width="2.5"
                            aria-hidden="true"
                          />
                          <span class="text-sm font-medium">{{ subscription.order_status }}</span>
                        </div>
                        <button
                          v-if="subscription.subscription_action_type === 0"
                          v-tooltip.focus.top="'Your subscription is on hold because the provider is reviewing your order. It will automatically become active once the prescription is issued.'"
                          class="text-gray-600 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full p-1"
                          aria-label="More information about subscription status"
                        >
                          <IconInfoCircle
                            class="h-5 w-5"
                            stroke-width="2"
                            aria-hidden="true"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="col-span-10 md:col-span-8">
                    <div
                      v-if="subscription.subscription_action_type === 1"
                      class="w-full bg-red-100 text-center rounded-lg px-3 py-1.5 text-sm font-medium text-red-700 mb-3"
                    >
                      Your subscription is on hold due to a payment failure.
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4">
                      <div class="flex-grow">
                        <div class="font-medium text-xl">
                          {{ subscription.product_name }}
                        </div>
                        <div
                          v-if="subscription.category_name === 'WL'"
                          class="text-sm text-zinc-500"
                        >
                          <span class="block sm:inline">
                            {{ subscription.strength }} {{ subscription.strength_unit }}/weekly
                          </span>
                        </div>
                        <div
                          v-else
                          class="text-sm text-zinc-500"
                        >
                          <span class="block sm:inline">
                            {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                          </span>
                          <span class="block sm:inline">
                            ({{ subscription.subscription_interval * 30 }}-day
                            supply)</span>
                        </div>
                      </div>
                      <div
                        v-if="!isEmpty(subscription.current_next_refill_date)"
                        class="flex-grow-1"
                      >
                        <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                          Next Refill Date
                        </div>
                        <div class="text-zinc-900">
                          {{ subscription.current_next_refill_date }}
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="subscription.category_name === 'WL'"
                      class="flex flex-col sm:flex-row gap-4 mt-4"
                    >
                      <div
                        v-if="subscription.subscribed_on"
                        class="flex-grow flex flex-col px-3 py-2 bg-sky-100 rounded-lg"
                      >
                        <div class="text-zinc-600 font-medium text-sm">
                          Subscribed On
                        </div>
                        <div class="text-zinc-900 text-base font-medium">
                          {{ subscription.subscribed_on }}
                        </div>
                      </div>
                      <div
                        v-if="subscription.next_follow_up_visit_date"
                        class="flex-grow flex flex-col px-3 py-2 bg-gray-100 rounded-lg"
                      >
                        <div class="text-zinc-600 font-medium text-sm">
                          Next Follow-up
                        </div>
                        <div class="text-zinc-900 text-base font-medium">
                          {{ subscription.next_follow_up_visit_date }}
                        </div>
                      </div>
                      <div class="flex-grow flex flex-col px-3 py-2 bg-green-100 rounded-lg">
                        <div class="text-zinc-600 font-medium text-sm">
                          Current Dosage
                        </div>
                        <div class="text-zinc-900 text-base font-medium">
                          {{ subscription.dose_month === 1 ? `Month ${subscription.dose_month} - Initial` : subscription.dose_month === 5 ? 'Max' : `Month ${subscription.dose_month}` }}
                        </div>
                      </div>
                    </div>
                    <div
                      v-else
                      class="flex flex-col sm:flex-row gap-4 mt-4"
                    >
                      <div class="flex-grow flex flex-col px-3 py-2 bg-sky-100 rounded-lg">
                        <div class="text-zinc-600 font-medium text-sm">
                          Total Refills
                        </div>
                        <div class="text-zinc-900 text-base font-medium">
                          {{ subscription.total_refills_allowed }}
                        </div>
                      </div>
                      <div class="flex-grow flex flex-col px-3 py-2 bg-gray-100 rounded-lg">
                        <div class="text-zinc-600 font-medium text-sm">
                          Used Refills
                        </div>
                        <div class="text-zinc-900 text-base font-medium">
                          {{ subscription.total_refill_used }}
                        </div>
                      </div>
                      <div class="flex-grow flex flex-col px-3 py-2 bg-green-100 rounded-lg">
                        <div class="text-zinc-600 font-medium text-sm">
                          Refills Remain
                        </div>
                        <div class="text-zinc-900 text-base font-medium">
                          {{ subscription.remain_refill }}
                        </div>
                      </div>
                    </div>
                    <hr class="my-4">
                    <!-- Subscription Actions -->
                    <div class="flex flex-col sm:flex-row gap-4">
                      <TwButton
                        class="!py-3 min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        variant="secondary"
                        :aria-label="`Manage subscription for ${subscription.product_name}`"
                        @click="manageSubscription(subscription.id)"
                      >
                        <span>Manage</span>
                        <IconChevronRight
                          class="h-4 w-4 ms-1"
                          stroke-width="2"
                          aria-hidden="true"
                        />
                      </TwButton>
                      <TwButton
                        v-if="Boolean(subscription?.is_follow_up_visit_enable)"
                        class="min-h-[44px] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        :aria-label="`Start follow-up visit for ${subscription.product_name}`"
                        @click="startFollowupVisit(subscription)"
                      >
                        Start Follow-up Visit
                      </TwButton>
                    </div>
                  </div>
                </div>
              </article>
            </div>
          </div>
        </div>

        <!-- Start Subscriptions -->
        <section
          class="grid grid-cols-1 lg:grid-cols-3 gap-5"
          aria-label="Available treatments"
        >
          <article class="bg-gradient-to-b from-blue-400 via-blue-300 to-cyan-100 p-5 rounded-[20px] min-h-64 flex flex-col justify-between focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 transition-all duration-200 hover:shadow-lg">
            <div>
              <h5 class="text-2xl font-semibold text-zinc-900 mb-2">
                Sexual health
              </h5>
              <p class="text-sm text-zinc-700 font-medium max-w-[65%]">
                Regain your confidence and have the sexual life you've always wanted.
              </p>
            </div>
            <div class="flex -mt-6">
              <TwButton
                v-if="getTreatmentType('ed') && isTreatmentAvailable('ed')"
                class="h-fit self-end"
                :loading="btnLoading === 'ed'"
                :disabled="!isEmpty(btnLoading)"
                loading-text="Starting..."
                @click="startTreatment('ed')"
              >
                <span>Start&nbsp;Now</span>
                <IconBolt
                  class="h-[14px] w-4 ms-1"
                  stroke-width="1"
                  fill="currentColor"
                />
              </TwButton>
              <TwButton
                v-else
                v-tooltip.top="{
                  value: startEDTooltip,
                  showDelay: 200,
                }"
                class="h-fit self-end"
                disabled
              >
                <span>Start&nbsp;Now</span>
                <IconBolt
                  class="h-[14px] w-4 ms-1"
                  stroke-width="1"
                  fill="currentColor"
                />
              </TwButton>

              <div class="w-fit ms-auto">
                <img
                  src="@/assets/user/images/ed-pills.png"
                  class="w-full h-36 object-cover -me-8"
                  alt="ED Pills"
                >
              </div>
            </div>
          </article>
        </section>
      </div>

      <div class="flex-1 flex flex-col justify-between bg-gradient-to-b from-green-400 via-green-300 to-lime-100 p-5 rounded-[20px] min-h-64 min-w-72">
        <div>
          <h5 class="text-2xl font-semibold text-zinc-900 mb-2">
            Hair Health
          </h5>
          <p class="text-sm text-zinc-700 font-medium max-w-[65%]">
            Address hair loss at the root with prescription medication.
          </p>
        </div>
        <div class="flex -mt-6">
          <TwButton
            v-if="getTreatmentType('hl') && isTreatmentAvailable('hl')"
            class="h-fit self-end"
            :loading="btnLoading === 'hl'"
            :disabled="!isEmpty(btnLoading)"
            loading-text="Starting..."
            @click="startTreatment('hl')"
          >
            <span>Start&nbsp;Now</span>
            <IconBolt
              class="h-[14px] w-4 ms-1"
              stroke-width="1"
              fill="currentColor"
            />
          </TwButton>
          <TwButton
            v-else
            v-tooltip.top="{
              value: startHLTooltip,
              showDelay: 200,
            }"
            class="h-fit self-end"
            disabled
          >
            <span>Start&nbsp;Now</span>
            <IconBolt
              class="h-[14px] w-4 ms-1"
              stroke-width="1"
              fill="currentColor"
            />
          </TwButton>

          <div class="w-fit ms-auto">
            <img
              src="@/assets/user/images/hl-pills.png"
              class="w-full h-40 object-cover -me-8 -mb-4"
              alt="ED Pills"
            >
          </div>
        </div>
      </div>

      <div class="flex-1 flex flex-col justify-between bg-gradient-to-b from-red-400 via-red-300 to-[#FFE5C7] p-5 rounded-[20px] min-h-64 min-w-72">
        <div>
          <h5 class="text-2xl font-semibold text-zinc-900 mb-2">
            Weight Loss
          </h5>
          <p class="text-sm text-zinc-700 font-medium max-w-[65%]">
            Lose up to 15% of your body weight with cutting-edge treatments.
          </p>
        </div>
        <div class="flex -mt-6">
          <TwButton
            v-if="isTreatmentAvailable('wl') && !isSubscriptionActive('wl')"
            class="h-fit self-end"
            :loading="btnLoading === 'wl'"
            :disabled="!isEmpty(btnLoading)"
            loading-text="Starting..."
            @click="startTreatment('wl')"
          >
            <span>Start&nbsp;Now</span>
            <IconBolt
              class="h-[14px] w-4 ms-1"
              stroke-width="1"
              fill="currentColor"
            />
          </TwButton>
          <TwButton
            v-else
            v-tooltip.top="{
              value: startWLTooltip,
              showDelay: 200,
            }"
            class="h-fit self-end"
            disabled
          >
            <span>Start&nbsp;Now</span>
            <IconBolt
              class="h-[14px] w-4 ms-1"
              stroke-width="1"
              fill="currentColor"
            />
          </TwButton>
          <div class="w-fit ms-auto">
            <img
              src="@/assets/user/images/wl-kit.png"
              class="w-full h-40 object-cover -me-8 -mb-2"
              alt="ED Pills"
            >
          </div>
        </div>
        </article>
        </section>

        <!-- No Subscriptions View -->
        <div
          v-if="activeSubscriptions.length === 0 && pastSubscriptions.length === 0 && !apiErrors.activeSubscriptions && !apiErrors.pastSubscriptions"
          class="bg-white p-5 rounded-[20px] text-center py-12"
        >
          <div class="max-w-md mx-auto">
            <IconCircleDot class="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              No Subscriptions Yet
            </h3>
            <p class="text-gray-600 mb-6">
              You currently do not have any active or past subscriptions. Start your health journey by choosing a treatment below.
            </p>
            <div class="text-sm text-gray-500">
              Need help? Contact our support team for assistance.
            </div>
          </div>
        </div>

        <!-- Error State for Past Subscriptions -->
        <div
          v-if="apiErrors.pastSubscriptions && pastSubscriptions.length === 0"
          class="bg-white p-5 rounded-[20px] border-l-4 border-red-500"
          role="alert"
          aria-live="polite"
        >
          <div class="flex items-start">
            <IconExclamationTriangle class="h-6 w-6 text-red-500 me-3 mt-1 flex-shrink-0" />
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-red-800 mb-2">
                Failed to Load Past Subscriptions
              </h3>
              <p class="text-red-700 mb-4">
                {{ apiErrors.pastSubscriptions }}
              </p>
              <TwButton
                variant="secondary"
                class="!border-red-500 !text-red-700 hover:!bg-red-50"
                :loading="retryAttempts.pastSubscriptions > 0"
                loading-text="Retrying..."
                @click="retryFetchPastSubscriptions"
              >
                <IconRefresh class="h-4 w-4 me-2" />
                Try Again
              </TwButton>
            </div>
          </div>
        </div>

        <!-- Past Subscriptions -->
        <div
          v-else-if="pastSubscriptions.length > 0"
          class="bg-white p-5 rounded-[20px]"
        >
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-semibold text-gray-900">
                Past Subscriptions
              </h2>
              <p class="text-sm text-gray-600 mt-1">
                {{ pastTotalRecords }} total {{ pastTotalRecords === 1 ? 'subscription' : 'subscriptions' }} • Showing {{ pastSubscriptions.length }}
              </p>
            </div>
            <div
              v-if="pastCurrentPage < pastTotalPage"
              class="text-sm text-gray-500"
            >
              Page {{ pastCurrentPage }} of {{ pastTotalPage }}
            </div>
          </div>
          <!-- List items -->
          <div class="space-y-4">
            <div
              v-for="subscription in pastSubscriptions"
              :key="subscription.id"
              class="px-3.5 py-4 rounded-3xl border !border-gray-400 max-md:max-w-full"
            >
              <div class="grid grid-cols-10 gap-4">
                <div class="col-span-10 md:col-span-2">
                  <div class="flex flex-col sm:items-center">
                    <div class="w-full bg-gray-100 flex justify-center rounded-2xl border !border-gray-200 mb-4">
                      <img
                        loading="lazy"
                        :src="subscription.product_img"
                        class="self-center aspect-square w-52 rounded-2xl"
                        alt=""
                      />
                    </div>
                    <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 font-bold whitespace-nowrap rounded-2xl bg-opacity-10 ${getSubscriptionStatus(subscription.status).bgColor} ${getSubscriptionStatus(subscription.status).textColor}`">
                      <component
                        :is="getSubscriptionStatus(subscription.status).icon"
                        :class="`shrink-0 self-center w-[20px] h-[20px] text-white rounded-full p-px ${getSubscriptionStatus(subscription.status).bgColor}`"
                        stroke-width="3"
                      />
                      <span>{{ subscription.order_status }}</span>
                    </div>
                  </div>
                </div>
                <div class="col-span-10 md:col-span-8">
                  <div
                    v-if="
                      subscription.status === 3
                        && !isEmpty(subscription.is_renewed_prescription)
                        && subscription.is_renewed_prescription === 1
                    "
                    class="w-full bg-red-100 text-center rounded-lg px-3 py-2 text-sm font-medium text-red-700 mb-3 flex flex-wrap justify-center items-center gap-3"
                  >
                    <span>Your Prescription is expired.</span>
                    <TwButton
                      v-if="
                        subscription.status === 3
                          && !isEmpty(subscription.is_renewed_prescription)
                          && subscription.is_renewed_prescription === 1
                      "
                      class="!py-2"
                      loading-text="Please wait..."
                      :loading="btnRenewPrescriptionLoading === subscription.id"
                      :disabled="!isEmpty(btnRenewPrescriptionLoading)"
                      @click="handleRenewPrescription(subscription)"
                    >
                      Renew Now
                    </TwButton>
                  </div>
                  <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-grow">
                      <div class="font-medium text-xl">
                        {{ subscription.product_name }}
                      </div>
                      <div
                        v-if="subscription.category_name === 'WL'"
                        class="text-sm text-zinc-500"
                      >
                        <span class="block sm:inline">
                          {{ subscription.strength }} {{ subscription.strength_unit }}/weekly
                        </span>
                      </div>
                      <div
                        v-else
                        class="text-sm text-zinc-500"
                      >
                        <span class="block sm:inline">
                          {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                        </span>
                        <span class="block sm:inline"> ({{ subscription.subscription_interval * 30 }}-day supply)</span>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="subscription.category_name === 'WL'"
                    class="flex flex-col sm:flex-row gap-4 mt-4"
                  >
                    <div
                      v-if="subscription.subscribed_on"
                      class="flex-grow flex flex-col px-3 py-2 bg-sky-100 rounded-lg"
                    >
                      <div class="text-zinc-600 font-medium text-sm">
                        Subscribed On
                      </div>
                      <div class="text-zinc-900 text-base font-medium">
                        {{ subscription.subscribed_on }}
                      </div>
                    </div>
                    <div
                      v-if="subscription.canceled_at"
                      class="flex-grow flex flex-col px-3 py-2 bg-gray-100 rounded-lg"
                    >
                      <div class="text-zinc-600 font-medium text-sm">
                        Canceled At
                      </div>
                      <div class="text-zinc-900 text-base font-medium">
                        {{ subscription.canceled_at }}
                      </div>
                    </div>
                    <div class="flex-grow flex flex-col px-3 py-2 bg-green-100 rounded-lg">
                      <div class="text-zinc-600 font-medium text-sm">
                        Dosage
                      </div>
                      <div class="text-zinc-900 text-base font-medium">
                        {{ subscription.dose_month === 1 ? `Month ${subscription.dose_month} - Initial` : subscription.dose_month === 5 ? 'Max' : `Month ${subscription.dose_month}` }}
                      </div>
                    </div>
                  </div>
                  <div
                    v-else
                    class="flex flex-col sm:flex-row gap-4 mt-4"
                  >
                    <div class="flex-grow flex flex-col px-3 py-2 bg-sky-100 rounded-lg">
                      <div class="text-zinc-600 font-medium text-sm">
                        Total Refills
                      </div>
                      <div class="text-zinc-900 text-base font-medium">
                        {{ subscription.total_refills_allowed }}
                      </div>
                    </div>
                    <div class="flex-grow flex flex-col px-3 py-2 bg-gray-100 rounded-lg">
                      <div class="text-zinc-600 font-medium text-sm">
                        Used Refills
                      </div>
                      <div class="text-zinc-900 text-base font-medium">
                        {{ subscription.total_refill_used }}
                      </div>
                    </div>
                    <div class="flex-grow flex flex-col px-3 py-2 bg-green-100 rounded-lg">
                      <div class="text-zinc-600 font-medium text-sm">
                        Refills Remain
                      </div>
                      <div class="text-zinc-900 text-base font-medium">
                        {{ subscription.remain_refill }}
                      </div>
                    </div>
                  </div>
                  <hr class="my-4">
                  <div class="flex flex-col sm:flex-row gap-4">
                    <TwButton
                      class="!py-2"
                      variant="secondary"
                      @click="manageSubscription(subscription.id)"
                    >
                      <span>View</span>
                      <IconChevronRight
                        class="h-4 w-4 ms-1"
                        stroke-width="2"
                      />
                    </TwButton>
                    <TwButton
                      v-if="!isEmpty(subscription.is_reactive_canceled_subscription) && subscription.is_reactive_canceled_subscription === 1"
                      @click="() => {
                        subscriptionToRestart = subscription
                        isRestartModalVisible = true
                      }"
                    >
                      Reactivate Subscription
                    </TwButton>
                  </div>
                </div>
              </div>
            </div>

            <SubscriptionItemSkeleton v-if="pastSkeletonLoading" />

            <div
              v-if="pastCurrentPage < pastTotalPage"
              class="text-center"
            >
              <TwButton
                class="text-xs !py-2"
                @click="handlePastSubscriptionPagination"
              >
                See More
              </TwButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Restart Subscription Modal -->
  <div
    v-if="isRestartModalVisible"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <div class="text-center py-6 px-4">
            <h5 class="text-2xl !text-gray-800 font-semibold mb-1">
              Reactivate your Subscription
            </h5>
            <p class="text-sm text-gray-700 mb-6">
              Subscription for the following medication will be reactivated
            </p>
            <div class="w-full mb-8 border !border-gray-200 p-3 !rounded-xl text-start">
              <div class="flex align-center gap-4">
                <div class="flex justify-center rounded-2xl">
                  <img
                    loading="lazy"
                    :src="subscriptionToRestart.product_img"
                    class="self-center aspect-square w-24 h-24 rounded-2xl"
                    alt=""
                  />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-xl mb-1">
                    {{ subscriptionToRestart.product_name }}
                  </div>
                  <div class="text-zinc-500 text-base">
                    <span>
                      {{ subscriptionToRestart.strength }} {{ subscriptionToRestart.strength_unit }} x {{ subscriptionToRestart.qty * subscriptionToRestart.subscription_interval }} units
                    </span>
                    <span class="block">({{ subscriptionToRestart.subscription_interval * 30 }}-day supply)</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex justify-center gap-3">
              <TwButton
                :disabled="btnReactivateLoading"
                variant="secondary"
                class="w-full"
                @click="isRestartModalVisible = false"
              >
                Cancel
              </TwButton>
              <TwButton
                class="w-full"
                :loading="btnReactivateLoading"
                @click="reactivateSubscription(subscriptionToRestart?.id)"
              >
                Confirm
              </TwButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced focus styles for better accessibility */
.focus\:ring-2:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Improved button hover states */
.hover\:scale-105:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* Loading animation for skeleton */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
