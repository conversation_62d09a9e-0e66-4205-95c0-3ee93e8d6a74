import { clearRetrySession } from '@/utils/sessionHelpers'
import guest from './middleware/guest'
import user from './middleware/user'
import { handleStartWLFollowUpFromMail, validateWLFollowupVisit } from './validators/startWLFollowup'
import { validateResetPasswordForUser } from './validators/validateResetPassword'
import { validateActiveVisit } from './validators/validateVisit'
import { validatePendingVisit } from './validators/validatePendingVisit'

const routes = [
  {
    path: '/',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      {
        path: '/',
        redirect: '/login',

        // name: 'home',
        // component: () => import('@/views/user/Home.vue'),
        // meta: {
        //   pageTitle: 'Quick Links',
        // },
      },

      {
        path: '/start-ed-consult',
        name: 'ed-start-consult',
        component: () => import('@/views/user/ed/SalesPage.vue'),
        meta: {
          pageTitle: 'Start your ED visit',
        },
        beforeEnter: clearRetrySession,
      },

      {
        path: '/start-hl-consult',
        name: 'hl-start-consult',
        component: () => import('@/views/user/hl/SalesPage.vue'),
        meta: {
          pageTitle: 'Start your Hair Treatment visit',
        },
        beforeEnter: clearRetrySession,
      },

      {
        path: '/start-wl-consult',
        name: 'wl-start-consult',
        component: () => import('@/views/user/wl/SalesPage.vue'),
        meta: {
          pageTitle: 'Start your Weight Loss visit',
        },
        beforeEnter: clearRetrySession,
      },

      {
        path: '/login',
        name: 'user-login',
        component: () => import('@/views/user/auth/Login.vue'),
        meta: {
          pageTitle: 'Login',
          middleware: [guest],
        },
      },
      {
        path: '/forgot-password',
        name: 'user-forgot-password',
        component: () => import('@/views/user/auth/ForgotPassword.vue'),
        meta: {
          pageTitle: 'Forgot your Password?',
          middleware: [guest],
        },
      },
      {
        path: '/reset-password',
        name: 'user-reset-password',
        component: () => import('@/views/user/auth/ResetPassword.vue'),
        meta: {
          pageTitle: 'Create a new password for your account',
          middleware: [guest],
        },
        beforeEnter: validateResetPasswordForUser,
      },

      // Sign up routes
      {
        path: '/:visitType/visit/medical-questions/signup',
        name: 'user-signup',
        component: () => import('@/views/user/auth/SignUpPage.vue'),
        meta: {
          pageTitle: 'Let\'s create your account',
        },
      },
      {
        path: '/:visitType/visit/step-1',
        name: 'user-signup-profile',
        component: () => import('@/views/user/auth/BasicDetailsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
        },
      },
      {
        path: '/:visitType/visit/sms-opt-in',
        name: 'user-sms-opt-in',
        component: () => import('@/views/user/auth/SmsOptInPage.vue'),
        meta: {
          pageTitle: 'Opt-in for SMS notifications',
          middleware: [user],
        },
      },

      // common routes
      {
        path: '/:visitType/visit/shipping-details',
        name: 'visit-shipping',
        component: () => import('@/views/user/common-pages/ShippingDetailsPage.vue'),
        meta: {
          pageTitle: 'Enter your shipping details',
          middleware: [user],
        },
      },
      {
        path: '/:visitType/visit/purchase-success',
        name: 'visit-purchase-success',
        component: () => import('@/views/user/common-pages/CheckoutCompletedPage.vue'),
        meta: {
          pageTitle: 'Visit created successfully',
          middleware: [user],
        },
      },
      {
        path: '/:visitType/visit/photo-upload-id',
        name: 'visit-photo-upload-id',
        component: () => import('@/views/user/common-pages/UploadGovtIdPage.vue'),
        meta: {
          pageTitle: 'Upload Government issued ID',
          middleware: [user],
        },
      },
      {
        path: '/:visitType/visit/photo-upload-selfie',
        name: 'visit-photo-upload-selfie',
        component: () => import('@/views/user/common-pages/UploadSelfiePage.vue'),
        meta: {
          pageTitle: 'Upload your selfie',
          middleware: [user],
        },
      },
      {
        path: '/:visitType/visit/photo-upload-full-body',
        name: 'visit-photo-upload-full-body',
        component: () => import('@/views/user/common-pages/UploadFullBodyPage.vue'),
        meta: {
          pageTitle: 'Upload your full body photo',
          middleware: [user],
        },
      },
      {
        path: '/:visitType/visit/reactivate',
        name: 'visit-reactivate',
        component: () => import('@/views/user/common-pages/ReactivateSubscription.vue'),
        meta: {
          pageTitle: 'Reactivate your subscription',
          middleware: [user],
        },
      },
      {
        path: '/pending-visit/:category/:id',
        name: 'complete-pending-visit',
        component: () => import('@/views/user/common-pages/CompletePendingVisit.vue'),
        beforeEnter: validatePendingVisit,
        meta: {
          pageTitle: 'Complete your pending visit',
          middleware: [user],
        },
      },

      // Error
      {
        path: '/error-something-wrong',
        name: 'error-something-wrong',
        component: () => import('@/views/user/error/ErrorPage.vue'),
        meta: {
          pageTitle: 'Something went wrong',
        },
      },
    ],
  },

  // ED Visit routes
  {
    path: '/ed',
    redirect: '/start-ed-consult',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      // new visit questions
      {
        path: '/ed/visit/medical-questions/:slug',
        name: 'ed-questions',
        component: () => import('@/views/user/ed/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
        },
        beforeEnter: (to, from, next) => validateActiveVisit('ed', to, from, next),
      },

      // follow up visit questions
      {
        path: '/ed/visit/followup/medical-questions/:slug',
        name: 'ed-followup-questions',
        component: () => import('@/views/user/ed/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Answer follow-up questions to continue your treatment',
          middleware: [user],
        },
      },

      // renew visit questions
      {
        path: '/ed/visit/renew/medical-questions/:slug',
        name: 'ed-questions-renew',
        component: () => import('@/views/user/ed/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
          middleware: [user],
        },
        beforeEnter: (to, from, next) => validateActiveVisit('ed', to, from, next),
      },

      {
        path: '/ed/visit/questions/complete',
        name: 'ed-questions-complete',
        component: () => import('@/views/user/common-pages/QuestionsCompletedPage.vue'),
        meta: {
          pageTitle: 'Woo-hoo! you answered all the questions',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/select/product',
        name: 'ed-visit-select-product',
        component: () => import('@/views/user/ed/SelectProductPage.vue'),
        meta: {
          pageTitle: 'Select your choice of drug',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/select/product-options',
        name: 'ed-visit-select-product-options',
        component: () => import('@/views/user/ed/SelectProductTypePage.vue'),
        meta: {
          pageTitle: 'Select your preferred choice',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/select/frequency',
        name: 'ed-visit-select-frequency',
        component: () => import('@/views/user/ed/SelectFrequencyPage.vue'),
        meta: {
          pageTitle: 'Select how you plan to use',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/select/dose-strength',
        name: 'ed-visit-select-strength',
        component: () => import('@/views/user/ed/SelectDoseStrengthPage.vue'),
        meta: {
          pageTitle: 'Select how much strength you need',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/select/dose-quantity',
        name: 'ed-visit-select-quantity',
        component: () => import('@/views/user/ed/SelectDoseQtyPage.vue'),
        meta: {
          pageTitle: 'Select how many times a month you plan to use',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/product/recommended',
        name: 'ed-visit-product-recommended',
        component: () => import('@/views/user/ed/SelectRecommendedPage.vue'),
        meta: {
          pageTitle: 'Continue with recommended choice',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/current-treatment',
        name: 'ed-visit-current-treatment',
        component: () => import('@/views/user/ed/CurrentTreatmentPage.vue'),
        meta: {
          pageTitle: 'Continue with current treatment',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/checkout',
        name: 'ed-visit-checkout',
        component: () => import('@/views/user/ed/CheckoutPage.vue'),
        meta: {
          pageTitle: 'Checkout',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/:orderId/success',
        name: 'ed-visit-order-success',
        component: () => import('@/views/user/common-pages/OrderSuccessPage.vue'),
        meta: {
          pageTitle: 'ED visit created successfully',
          middleware: [user],
        },
      },
      {
        path: '/ed/visit/exists',
        name: 'ed-visit-exists',
        component: () => import('@/views/user/common-pages/VisitExistsPage.vue'),
        meta: {
          pageTitle: 'ED visit already active',
          middleware: [user],
        },
      },
      {
        path: '/ed/treatment-not-available',
        name: 'ed-treatment-not-available',
        component: () => import('@/views/user/common-pages/TreatmentNotAvailable.vue'),
        meta: {
          pageTitle: 'ED treatment not available',
          middleware: [user],
        },
      },

      // Renew ED Visit from email
      {
        path: '/ed/renew-rx/:subscriptionId/start',
        name: 'ed-renew-start',
        component: () => import('@/views/user/ed/ValidateRenew.vue'),
        meta: {
          pageTitle: 'Renew Prescription',
          middleware: [user],
        },
        beforeEnter: (to, from, next) => validateActiveVisit('ed', to, from, next),
      },
    ],
  },

  // HL Visit routes
  {
    path: '/hl',
    redirect: '/start-hl-consult',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      // new visit questions
      {
        path: '/hl/visit/medical-questions/:slug',
        name: 'hl-questions',
        component: () => import('@/views/user/hl/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
        },
        beforeEnter: (to, from, next) => validateActiveVisit('hl', to, from, next),
      },

      // followup visit questions
      {
        path: '/hl/followup/medical-questions/:slug',
        name: 'hl-followup-questions',
        component: () => import('@/views/user/hl/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
          middleware: [user],
        },
      },

      // renew visit questions
      {
        path: '/hl/renew-rx/medical-questions/:slug',
        name: 'hl-questions-renew',
        component: () => import('@/views/user/hl/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
          middleware: [user],
        },
        beforeEnter: (to, from, next) => validateActiveVisit('hl', to, from, next),
      },
      {
        path: '/hl/visit/questions/complete',
        name: 'hl-questions-complete',
        component: () => import('@/views/user/common-pages/QuestionsCompletedPage.vue'),
        meta: {
          pageTitle: 'Woo-hoo! you answered all the questions',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/product/recommended',
        name: 'hl-visit-product-recommended',
        component: () => import('@/views/user/hl/SelectRecommendedPage.vue'),
        meta: {
          pageTitle: 'Continue with recommended choice',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/select/product',
        name: 'hl-visit-select-product',
        component: () => import('@/views/user/hl/SelectProductPage.vue'),
        meta: {
          pageTitle: 'Select your choice of drug',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/select/dose-strength',
        name: 'hl-visit-select-strength',
        component: () => import('@/views/user/hl/SelectDoseStrengthPage.vue'),
        meta: {
          pageTitle: 'Select how much strength you need',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/current-treatment',
        name: 'hl-visit-current-treatment',
        component: () => import('@/views/user/hl/CurrentTreatmentPage.vue'),
        meta: {
          pageTitle: 'Continue with current treatment',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/checkout',
        name: 'hl-visit-checkout',
        component: () => import('@/views/user/hl/CheckoutPage.vue'),
        meta: {
          pageTitle: 'Checkout',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/:orderId/success',
        name: 'hl-visit-order-success',
        component: () => import('@/views/user/common-pages/OrderSuccessPage.vue'),
        meta: {
          pageTitle: 'Hair treatment visit created successfully',
          middleware: [user],
        },
      },
      {
        path: '/hl/visit/exists',
        name: 'hl-visit-exists',
        component: () => import('@/views/user/common-pages/VisitExistsPage.vue'),
        meta: {
          pageTitle: 'Hair treatment visit already exists',
          middleware: [user],
        },
      },
      {
        path: '/hl/treatment-not-available',
        name: 'hl-treatment-not-available',
        component: () => import('@/views/user/common-pages/TreatmentNotAvailable.vue'),
        meta: {
          pageTitle: 'Hair Loss treatment not available',
          middleware: [user],
        },
      },

      // Renew HL Visit from email
      {
        path: '/hl/renew-rx/:subscriptionId/start',
        name: 'hl-renew-start',
        component: () => import('@/views/user/hl/ValidateRenew.vue'),
        meta: {
          pageTitle: 'Renew Prescription',
          middleware: [user],
        },
        beforeEnter: (to, from, next) => validateActiveVisit('hl', to, from, next),
      },
    ],
  },

  // WL Visit routes
  {
    path: '/wl',
    redirect: '/start-wl-consult',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      {
        path: '/wl/visit/medical-questions/:slug',
        name: 'wl-questions',
        component: () => import('@/views/user/wl/QuestionsPage.vue'),
        meta: {
          pageTitle: 'Find out if you qualify for treatment by completing our questions',
        },
        beforeEnter: async (to, from, next) => {
          return await validateActiveVisit('wl', to, from, next)
        },
      },
      {
        path: '/wl/visit/recommended-treatment',
        name: 'wl-products',
        component: () => import('@/views/user/wl/ProductsPage.vue'),
        meta: {
          pageTitle: 'Weight Loss Treatment Recommendations',
          middleware: [user],
        },
      },
      {
        path: '/wl/visit/checkout',
        name: 'wl-visit-checkout',
        component: () => import('@/views/user/wl/CheckoutPage.vue'),
        meta: {
          pageTitle: 'Checkout | Weight Loss Treatment',
          middleware: [user],
        },
      },
      {
        path: '/wl/visit/:orderId/success',
        name: 'wl-visit-order-success',
        component: () => import('@/views/user/common-pages/OrderSuccessPage.vue'),
        meta: {
          pageTitle: 'Weight Loss visit created successfully',
          middleware: [user],
        },
      },

      {
        path: '/wl/visit/exists',
        name: 'wl-visit-exists',
        component: () => import('@/views/user/common-pages/VisitExistsPage.vue'),
        meta: {
          pageTitle: 'Weight Loss visit already exists',
          middleware: [user],
        },
      },
      {
        path: '/wl/treatment-not-available',
        name: 'wl-treatment-not-available',
        component: () => import('@/views/user/common-pages/TreatmentNotAvailable.vue'),
        meta: {
          pageTitle: 'Weight Loss treatment not available',
          middleware: [user],
        },
      },
      {
        path: '/wl/out-of-stock',
        name: 'wl-out-of-stock',
        component: () => import('@/views/user/wl/OutOfStockPage.vue'),
        meta: {
          pageTitle: 'Weight Loss product out of stock',
          middleware: [user],
        },
      },

      // follow up routes
      {
        path: '/wl-followup/visit/medical-questions/:slug',
        name: 'wl-followup-questions',
        component: () => import('@/views/user/wl/FollowupQuestionsPage.vue'),
        beforeEnter: validateWLFollowupVisit,
        meta: {
          pageTitle: 'Start you follow up treatment by completing our questions',
          middleware: [user],
        },
      },
      {
        path: '/wl-followup/visit/recommended-treatment',
        name: 'wl-followup-products',
        component: () => import('@/views/user/wl/FollowupProductsPage.vue'),
        meta: {
          pageTitle: 'Weight Loss Treatment Recommendations',
          middleware: [user],
        },
      },
      {
        path: '/wl-followup/visit/checkout',
        name: 'wl-followup-visit-checkout',
        component: () => import('@/views/user/wl/FollowupCheckoutPage.vue'),
        meta: {
          pageTitle: 'Checkout | Weight Loss Follow-Up Treatment',
          middleware: [user],
        },
      },
      {
        path: '/wl-followup/visit/:orderId/success',
        name: 'wl-followup-visit-order-success',
        component: () => import('@/views/user/common-pages/OrderSuccessPage.vue'),
        meta: {
          pageTitle: 'Weight Loss visit created successfully',
          middleware: [user],
        },
      },

      // followup misc routes
      {
        path: '/wl-followup/visit/start-from-mail/:subscriptionId',
        name: 'wl-followup-start-from-mail',
        beforeEnter: handleStartWLFollowUpFromMail,
        meta: {
          pageTitle: 'Validating weight loss follow up visit',
          middleware: [user],
        },
      },
    ],
  },

  // OTC Product Listing
  {
    path: '/otc',
    redirect: '/over-the-counter',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      {
        path: '/over-the-counter/:category?/:subCategory?',
        name: 'otc-products',
        component: () => import('@/views/user/otc/ProductListingPage.vue'),
        meta: {
          pageTitle: 'Over-the-Counter Products',
        },
      },
      {
        path: '/product/:productSlug',
        name: 'otc-product-details',
        component: () => import('@/views/user/otc/ProductDetailsPage.vue'),
        meta: {
          pageTitle: 'Product Details',
        },
      },
      {
        path: '/cart',
        name: 'user-cart',
        component: () => import('@/views/user/otc/CartPage.vue'),
        meta: {
          pageTitle: 'Shopping Cart',
        },
      },
      {
        path: '/checkout',
        name: 'otc-checkout',
        component: () => import('@/views/user/otc/checkout/CheckoutPage.vue'),
        meta: {
          pageTitle: 'Checkout',
        },
        children: [
          {
            path: 'account',
            name: 'otc-checkout-account',
            component: () => import('@/views/user/otc/checkout/steps/AccountStep.vue'),
            meta: {
              pageTitle: 'Account Information',
            },
          },
          {
            path: 'shipping',
            name: 'otc-checkout-shipping',
            component: () => import('@/views/user/otc/checkout/steps/ShippingStep.vue'),
            meta: {
              pageTitle: 'Shipping Information',
            },
          },
          {
            path: 'payment',
            name: 'otc-checkout-payment',
            component: () => import('@/views/user/otc/checkout/steps/PaymentStep.vue'),
            meta: {
              pageTitle: 'Payment Information',
            },
          },
        ],
      },
      {
        path: '/order/success/:orderId',
        name: 'otc-order-success',
        component: () => import('@/views/user/otc/checkout/OrderSuccessPage.vue'),
        meta: {
          pageTitle: 'Order Confirmed',
        },
      },
    ],
  },

  // User Dashboard
  {
    path: '/',
    component: () => import('@/layouts/AuthLayout.vue'),
    meta: {
      middleware: [user],
    },
    children: [
      {
        path: '/subscriptions',
        name: 'user-subscription',
        component: () => import('@/views/user/dashboard/subscription/SubscriptionPage.vue'),
        meta: {
          pageTitle: 'Manage your subscriptions',
        },
      },
      {
        path: '/subscriptions/:subscriptionId',
        name: 'user-manage-subscription',
        component: () => import('@/views/user/dashboard/subscription/SubscriptionManagePage.vue'),
        meta: {
          pageTitle: 'Manage your subscription',
        },
      },
      {
        path: '/orders',
        name: 'user-orders',
        component: () => import('@/views/user/dashboard/orders/OrdersPage.vue'),
        meta: {
          pageTitle: 'Manage your orders',
        },
      },
      {
        path: '/orders/:orderId',
        name: 'user-order-details',
        component: () => import('@/views/user/dashboard/orders/OrderDetailPage.vue'),
        meta: {
          pageTitle: 'Order Details',
        },
      },
      {
        path: '/orders/otc/:orderId',
        name: 'user-otc-order-details',
        component: () => import('@/views/user/dashboard/orders/OtcOrderDetailPage.vue'),
        meta: {
          pageTitle: 'OTC Order Details',
        },
      },
      {
        path: '/profile',
        name: 'user-profile',
        component: () => import('@/views/user/dashboard/profile/ProfilePage.vue'),
        meta: {
          pageTitle: 'Manage your profile',
        },
      },
    ],
  },

  {
    path: '/subscription/reactivation',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      {
        path: '/subscriptions/:subscriptionId/reactivation/:status',
        name: 'user-subscription-reactivation-status',
        component: () => import('@/views/user/common-pages/SubscriptionStatus.vue'),
        meta: {
          pageTitle: 'Subscription Reactivation',
          middleware: [user],
        },
      },
    ],
  },

  // Subscription Management additional options
  {
    path: '/subscription/:subscriptionId/manage',
    name: 'user-manage-subscription-additional',
    component: () => import('@/views/user/dashboard/subscription/SubscriptionManageAdditionalOptions.vue'),
    meta: {
      pageTitle: 'Manage your subscription',
    },
  },

  // Product/treatment overview
  {
    path: '/treatment/:productId/:category',
    name: 'user-treatment-overview',
    component: () => import('@/views/user/dashboard/treatment/OverviewPage.vue'),
    meta: {
      pageTitle: 'Manage your subscription',
    },
  },

  // Account support Q/A's
  {
    path: '/support/:slug?',
    name: 'account-support',
    component: () => import('@/views/user/support/SupportPage.vue'),
    meta: {
      pageTitle: 'Account Support & FAQs',
    },
  },

  // chat assistant
  // {
  //   path: '/chat',
  //   redirect: '/dr-smith',
  // },
  // {
  //   path: '/dr-smith',
  //   component: () => import('@/layouts/LandingLayout.vue'),
  //   children: [
  //     {
  //       path: '/dr-smith',
  //       name: 'assistant-chat',
  //       component: () => import('@/views/user/assistant/AssistantPage.vue'),
  //       meta: {
  //         pageTitle: 'Chat with Dr. Smith',
  //       },
  //     },
  //   ],
  // },
]

export default routes
